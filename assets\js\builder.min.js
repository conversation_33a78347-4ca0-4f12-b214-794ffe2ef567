WPForms.Admin.Builder.Providers.GoogleSheets=WPForms.Admin.Builder.Providers.GoogleSheets||function(e,n){const l={provider:"google-sheets",isReady:!1,$holder:null,$connections:null,Providers:{},templates:{config:["wpforms-google-sheets-builder-content-auth-error","wpforms-google-sheets-builder-content-conditionals","wpforms-google-sheets-builder-content-connection","wpforms-google-sheets-builder-content-connection-error","wpforms-google-sheets-builder-content-connection-conditionals","wpforms-google-sheets-builder-content-error","wpforms-google-sheets-builder-content-fields","wpforms-google-sheets-builder-content-sheet-select","wpforms-google-sheets-builder-content-new-account-advanced-form"],providerTemplates:{},load(){l.templates.providerTemplates=WPForms.Admin.Builder.Templates,l.templates.providerTemplates.add(l.templates.config)},render(e,o={}){return e="wpforms-"+l.provider+"-builder-content-"+e,l.templates.providerTemplates.get(e)(o)}},cache:{providerCache:{},getConnection(e){e=l.cache.providerCache.getById(l.provider,"connections",e);return _.isObject(e)?e:null},getSpreadsheets(){console.warn('WARNING! Function "WPForms.Admin.Builder.Providers.GoogleSheets.cache.getSpreadsheets()" has been deprecated');var e=l.cache.providerCache.get(l.provider,"spreadsheets");return _.isEmpty(e)?null:e},getSheets(e){e=l.cache.providerCache.getById(l.provider,"sheets",e);return _.isObject(e)?e:null},addToSheets(e,o){l.cache.providerCache.addTo(l.provider,"sheets",e,o)},getColumns(e,o){e=l.cache.providerCache.getById(l.provider,"columns",e);return _.isObject(e)&&_.has(e,o)&&_.isObject(e[o])?e[o]:null}},init(){const i="settings";wpf.getQueryString("view")===i&&n("#wpforms-panel-"+i).on("WPForms.Admin.Builder.Providers.ready",l.ready),n(e).on("wpformsPanelSwitched",function(e,o){o===i&&l.ready()})},ready(){l.isReady||(l.Providers=WPForms.Admin.Builder.Providers,l.cache.providerCache=l.Providers.cache,l.$holder=l.Providers.getProviderHolder(l.provider),l.$connections=l.$holder.find(".wpforms-builder-provider-connections"),l.templates.load(),gapi.load("picker"),l.bindUIActions(),l.bindTriggers(),l.processInitial(),l.isReady=!0)},bindUIActions(){n(e).on("click",".js-wpforms-google-sheets-setting-field-redirect-uri-copy",l.ui.account.copyUrlClick).on("click",".js-wpforms-google-sheets-change-mode",l.ui.account.switchToAdvancedMode).on("click",".wpforms-providers-account-add-modal #wpforms-google-sheets-sign-in .gsi-material-button",l.ui.account.add).on("wpformsFieldUpdate",l.ui.customFields.mapSelectFields),n("#wpforms-builder").on("wpformsSaved",l.ui.connection.refreshConnections),l.$holder.on("click",".wpforms-alert #wpforms-google-sheets-sign-in .gsi-material-button",l.ui.account.reconnect).on("connectionCreate",l.ui.connection.create).on("connectionDelete",l.ui.connection.delete).on("change",".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-type",l.ui.spreadsheetTypeField.changeType).on("click",".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id-choose",l.ui.spreadsheetField.openPicker).on("click",".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id-remove",l.ui.spreadsheetField.clear).on("input",".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",l.ui.spreadsheetField.changeSpreadsheet).on("input",".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",l.ui.docLink.changeFields).on("change",".js-wpforms-builder-google-sheets-provider-connection-sheet-id",l.ui.sheetField.changeSheet).on("change",".js-wpforms-builder-google-sheets-provider-connection-sheet-id",l.ui.docLink.changeFields).on("click",".js-wpforms-builder-google-sheets-provider-connection-fields-add",l.ui.customFields.addNewRow).on("change",".js-wpforms-builder-provider-connection-field-id",l.ui.customFields.changeFieldIdField).on("accountAddModal.onOpenBefore",l.ui.account.updatePopup)},bindTriggers(){l.$holder.on("connectionsDataLoaded",function(e,o){if(!_.isEmpty(o.connections))for(const i in o.connections)l.ui.connection.generate(o.connections[i])}),l.$holder.on("connectionGenerated",function(e,o){var i=l.ui.connection.getById(o.connection.id),i=(l.ui.connection.isNewConnection(o.connection)&&l.ui.connection.replaceConnectionIds(o.connection.id,i),l.cache.providerCache.get(l.provider,"non_editable_spreadsheets")||{});Object.prototype.hasOwnProperty.call(o.connection,"spreadsheet_id")&&Object.values(i).includes(o.connection.spreadsheet_id)&&l.ui.connection.lock(o.connection)})},processInitial(){l.$holder.prepend(l.tmpl.commonsHTML()),l.ui.connection.dataLoad()},ui:{toggleFieldVisibility(e,o,i=!1){var r=e.find("select, input");o?(r.removeClass("wpforms-disabled").removeClass("wpforms-required"),e.removeClass("wpforms-hidden")):(r.addClass("wpforms-disabled"),e.addClass("wpforms-hidden"),i&&r.addClass("wpforms-required"))},spreadsheetTypeField:{changeType(){var e=n(this),o=e.val(),e=e.closest(".wpforms-builder-provider-connection");"new"===o?l.ui.spreadsheetTypeField.createMode(e):l.ui.spreadsheetTypeField.existingMode(e)},createMode(e){var o=n(".wpforms-builder-google-sheets-provider-spreadsheet-existing",e),i=n(".wpforms-builder-google-sheets-provider-spreadsheet-name",e),r=n(".wpforms-builder-google-sheets-provider-sheet-id",e),t=r.find("select",e),e=n(".wpforms-builder-google-sheets-provider-connection-fields",e);l.ui.toggleFieldVisibility(o,!1,!0),l.ui.toggleFieldVisibility(r,!1,!0),l.ui.toggleFieldVisibility(i,!0),WPForms.Admin.Builder.SmartTags.initWidgets(e),e.removeClass("wpforms-hidden"),t.data("prev-value",t.val()).val("new").trigger("change")},existingMode(e){var o=n(".wpforms-builder-google-sheets-provider-spreadsheet-existing",e),i=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",e).val(),r=n(".wpforms-builder-google-sheets-provider-spreadsheet-name",e),t=n(".wpforms-builder-google-sheets-provider-sheet-id",e),s=t.find("select",e),e=n(".wpforms-builder-google-sheets-provider-connection-fields",e),d=s.data("prev-value");l.ui.toggleFieldVisibility(o,!0,!0),i&&l.ui.toggleFieldVisibility(t,!0,!0),l.ui.toggleFieldVisibility(r,!1),d||e.addClass("wpforms-hidden"),s.val(d).data("prev-value","").trigger("change")}},spreadsheetField:{openPicker(){const i=n(this).closest(".wpforms-builder-provider-connection");l.Providers.ajax.request(l.provider,{data:{task:"access_token_data_get"}}).done(function(e){(new google.picker.PickerBuilder).setOAuthToken(e.data.access_token).addView(new google.picker.DocsView(google.picker.ViewId.SPREADSHEETS).setMode(google.picker.DocsViewMode.LIST)).hideTitleBar().enableFeature(google.picker.Feature.NAV_HIDDEN).setCallback(function(e){var o;e.action===google.picker.Action.PICKED&&(o=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",i),e=e[google.picker.Response.DOCUMENTS][0][google.picker.Document.ID],o.val(e).trigger("input"))}).build().setVisible(!0)})},clear(){var e=n(this).closest(".wpforms-builder-provider-connection"),o=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",e);n(".wpforms-builder-google-sheets-provider-sheet-id select",e).val("").trigger("change"),o.val("").trigger("input")},changeSpreadsheet(){var e=n(this),o=e.val(),e=e.closest(".wpforms-builder-provider-connection"),i=n(".wpforms-builder-google-sheets-provider-sheet-id",e),r=n(".wpforms-builder-provider-connection-block-field-exiting-empty",e),t=n(".wpforms-builder-provider-connection-block-field-exiting-not-empty",e),s=n(".wpforms-builder-google-sheets-provider-spreadsheet-type",e);l.ui.connection.unlock(e),""===o||null===o?(l.ui.toggleFieldVisibility(s,!0),l.ui.toggleFieldVisibility(i,!1,!0),l.ui.toggleFieldVisibility(r,!0),l.ui.toggleFieldVisibility(t,!1)):(l.ui.toggleFieldVisibility(s,!1),l.ui.toggleFieldVisibility(i,!0,!0),l.ui.toggleFieldVisibility(r,!1),l.ui.toggleFieldVisibility(t,!0),(s=l.cache.getSheets(o))?l.tmpl.sheetField(s,e):l.ui.spreadsheetField.requestData(e))},requestData(o){const i=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",o).val();const r=n(".wpforms-builder-google-sheets-provider-sheet-id",o).find("select");l.Providers.ajax.request(l.provider,{data:{task:"spreadsheet_data_get",spreadsheet_id:i}}).done(function(e){_.isEmpty(e.data.sheets)||(l.cache.addToSheets(i,e.data.sheets),l.tmpl.sheetField(e.data.sheets,o),r.val("").trigger("change"))})},nonEditableModal(){l.modal(wpforms_builder.google_sheets_non_editable_spreadsheets)}},sheetField:{changeSheet(){var e=n(this),o=e.val(),e=e.closest(".wpforms-builder-provider-connection"),i=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",e),r=n(".wpforms-builder-google-sheets-provider-sheet-name",e),t=n(".wpforms-builder-google-sheets-provider-connection-fields",e);l.ui.toggleFieldVisibility(r,"new"===o),null!==o&&""!==o||t.addClass("wpforms-hidden"),i.val()&&l.ui.sheetField.requestData(e)},requestData(o){var e=n(".wpforms-builder-google-sheets-provider-sheet-id select",o).val();if(null!==e){var i=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",o).val();const t=n(".wpforms-builder-google-sheets-provider-connection-fields",o);var r=l.ui.customFields.getValue(o);t.removeClass("wpforms-hidden"),l.Providers.ajax.request(l.provider,{data:{task:"sheet_data_get",spreadsheet_id:i,sheet_id:e,custom_fields:r}}).done(function(e){if(!_.isEmpty(e.data.columns)){let i=!1;l.tmpl.customFields(o,e.data.columns,e.data.custom_fields),t.find(".wpforms-builder-provider-connection-fields-table-row").each(function(){var e,o=n(this);return 0===o.index()||(e=o.find(".wpforms-builder-provider-connection-field-name"),o=o.find(".wpforms-builder-provider-connection-field-id"),!e.val()&&o.val()?!(i=!0):void 0)}),WPForms.Admin.Builder.SmartTags.initWidgets(t),i&&l.modal(wpforms_builder.google_sheets_not_mapped_field_found)}})}}},customFields:{getValue(e){e=n(".wpforms-builder-google-sheets-provider-connection-fields",e);const r=[];return e.find("tr").each(function(){var e=n(this),o=n(".wpforms-builder-provider-connection-field-name",e).val();if(!o)return!0;var i=n(".wpforms-builder-provider-connection-field-id",e).val(),e=n(".wpforms-builder-provider-connection-field-value",e).val();r.push({name:o,field_id:i,value:e})}),r},addNewRow(e){e.preventDefault();var e=n(this).closest(".wpforms-builder-provider-connection-fields-table"),o=n("tr",e).last().clone(!0),i=n(".wpforms-builder-provider-connection-field-name",o),r=n(".wpforms-builder-provider-connection-field-id",o),t=n(".wpforms-builder-provider-connection-field-value",o),s=parseInt(/\[.+]\[.+]\[.+]\[(\d+)]/.exec(o.find(".wpforms-builder-provider-connection-field-name").attr("name"))[1],10)+1,d=n(".wpforms-field-option-row",o);l.ui.customFields.replaceFieldIndex(i,s),l.ui.customFields.replaceFieldIndex(r,s),l.ui.customFields.replaceFieldIndex(t,s),n("input",o).addClass("wpforms-smart-tags-enabled"),d.addClass("wpforms-hidden"),d.prev().attr("colspan",2),n(".js-wpforms-builder-provider-connection-fields-delete",o).removeClass("wpforms-hidden"),n("tbody",e).append(o.get(0)),WPForms.Admin.Builder.SmartTags.reinitWidgetInClone(null,o)},changeFieldIdField(){var e=n(this),o=e.val(),i=e.closest(".wpforms-builder-provider-connection-fields-table-row"),e=e.closest(".wpforms-builder-provider-connection-fields-table-column"),i=n(".wpforms-field-option-row",i);"custom"===o?(i.removeClass("wpforms-hidden"),e.removeAttr("colspan")):(i.addClass("wpforms-hidden"),e.attr("colspan",2))},replaceFieldIndex(e,o){var i=e.closest("label");e.attr("name",e.attr("name").replace(/\[custom_fields]\[(\d+)]/g,"[custom_fields]["+o+"]")).attr("id",e.attr("id").replace(/\d+$/g,o)).removeAttr("disabled").val(""),i.attr("for",i.attr("for").replace(/\d+$/g,o))},mapSelectFields(){n(".js-wpforms-builder-provider-connection-field-id").each(function(){var e=n(this),o=e.find("option:selected").val(),i=l.ui.customFields.getFormFields();l.ui.customFields.updateSelectOptions(e,i),o&&e.find('option[value="'+o+'"]').prop("selected",!0),n("#wpforms-builder").trigger("wpformsFieldSelectMapped",[e])})},getFormFields(){return wpf.getFields(void 0,!0,!0)},updateSelectOptions(e,o){var i=wpforms_builder.google_sheets_select_form_field;if(e.empty().append(n("<option>",{value:"",text:i})),o&&!n.isEmptyObject(o))for(const t in o){var r=l.ui.customFields.getFieldLabel(o[t],t);e.append(n("<option>",{value:o[t].id,text:r}))}e.append(n("<option>",{value:"custom",text:wpforms_builder.google_sheets_custom_value}))},getFieldLabel(e,o){return void 0!==e.label&&""!==e.label.toString().trim()?wpf.sanitizeHTML(e.label.toString().trim()):wpforms_builder.field+" #"+o}},account:{reconnect(e){e.preventDefault();e=n(this).closest(".wpforms-alert-buttons").find('input[type="hidden"]');wpforms_builder.exit_url=e.val(),WPFormsBuilder.formSave(!0)},add(){var e=n(this).closest(".jconfirm-content");if(!l.ui.account.isValidForm(e))return!1;const o=e.find(".wpforms-google-sheets-auth-error");var i=l.ui.account.getFormMode(e),r={mode:i};"advanced"===i&&(r.client_id=l.ui.account.getFieldValueByName("client_id",e),r.client_secret=l.ui.account.getFieldValueByName("client_secret",e)),l.Providers.ajax.request(l.provider,{data:{task:"account_save",data:r}}).done(function(e){e.success?(n(".wpforms-builder-provider-connections-save-lock",l.$holder).val(1),wpforms_builder.exit_url=e.data,WPFormsBuilder.formSave(!0)):(_.has(e,"data")&&o.html(e.data),o.show())})},isValidForm(e){var o,i,r,t=n(".wpforms-google-sheets-auth-required-error",e);return"pro"===l.ui.account.getFormMode(e)?(t.hide(),!0):(o=l.ui.account.getFieldValueByName("client_id",e),i=l.ui.account.getFieldValueByName("client_secret",e),r=n('input[name="client_id"]',e),e=n('input[name="client_secret"]',e),Boolean(o.length&&i.length)?(t.hide(),r.removeClass("wpforms-error"),e.removeClass("wpforms-error"),!0):(t.show(),r.addClass("wpforms-error"),e.addClass("wpforms-error"),!1))},getFormMode(e){return n('input[name="client_id"]',e).length?"advanced":"pro"},getFormContainer(e){return"advanced"===l.ui.account.getFormMode(e)?n(".wpforms-google-sheets-auth-custom",e):n(".wpforms-google-sheets-auth-pro",e)},getFieldValueByName(e,o){e=n('[name="'+e+'"]',o);return e.length?e.val().toString().trim():""},copyUrlClick(){const e=n(this);var o=e.closest(".wpforms-google-sheets-setting-field-redirect-uri-row");const i="wpforms-google-sheets-setting-field-redirect-uri-copy-success";o=n(".wpforms-google-sheets-setting-field-redirect-uri-input",o);o.select(),navigator.clipboard.writeText(o.val()).then(function(){e.addClass(i),setTimeout(function(){e.removeClass(i)},500)})},switchToAdvancedMode(e){e.preventDefault(),n.alert({title:wpforms_builder.google_sheets_advanced_form_title,content:l.templates.render("new-account-advanced-form"),icon:"fa fa-info-circle",type:"blue",animation:"none",closeAnimation:"none",buttons:{cancel:{text:wpforms_builder.google_sheets_advanced_form_cancel_button}},onOpenBefore(){this.$jconfirmBg.removeClass("jconfirm-bg"),this.$body.addClass("wpforms-providers-account-add-modal"),l.$holder.trigger("accountAddModal.onOpenBefore",[this])}})},updatePopup(e,o){var i=o.$content,i=l.ui.account.getFormMode(i),r="google_sheets_"+i+"_form_footer";"pro"===i&&o.$$add.remove(),o.$body.css("padding-bottom","63px").append('<div class="wpforms-google-sheets-auth-footer">'+wpforms_builder[r]+"</div>")},invalidAccountModal(e){l.modal(wpforms_builder.google_sheets_auth_failed);var o=l.Providers.getProviderHolder(l.provider),i=o.find(".wpforms-builder-provider-body"),r=o.find(".wpforms-builder-provider-connections-default"),o=o.find(".wpforms-builder-provider-title-add");i.prepend(l.templates.render("auth-error",{reauthUrl:e})),r.hide(),o.hide(),n(".wpforms-builder-provider-connections-save-lock",l.$holder).val(1)}},docLink:{changeFields(){var e=n(this).closest(".wpforms-builder-provider-connection");l.ui.docLink.update(e)},update(e){var o=n(".wpforms-builder-google-sheets-provider-spreadsheet-id input[type=radio]",e).val(),i=n(".wpforms-builder-google-sheets-provider-spreadsheet-id input[type=hidden]",e).val();i&&"new"!==o&&(o=n(".wpforms-builder-google-sheets-provider-spreadsheet-id a",e),e="new"!==(e=n(".wpforms-builder-google-sheets-provider-sheet-id select",e).val())&&e?e:0,o.attr("href",l.ui.docLink.getSpreadsheetURL(i,e)))},getSpreadsheetURL(e,o){return"https://docs.google.com/spreadsheets/d/{spreadsheetId}/edit#gid={sheetId}".replace("{spreadsheetId}",e).replace("{sheetId}",o)}},connection:{getById(e){return l.$holder.find('.wpforms-builder-provider-connection[data-connection_id="'+e+'"]')},create(e,o){var i=(new Date).getTime().toString(16),o={id:i,name:o,isNew:!0};l.cache.providerCache.addTo(l.provider,"connections",i,o),l.ui.connection.generate(o)},delete(e,o){var i=l.Providers.getProviderHolder(l.provider);o.closest(i).length&&(i=o.data("connection_id"),_.isString(i))&&l.cache.providerCache.deleteFrom(l.provider,"connections",i)},generate(o){l.ui.connection.replace(o);var i=l.ui.connection.getById(o.id);if(n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",i).trigger("input"),void 0!==o.spreadsheet_id&&""!==o.spreadsheet_id||l.tmpl.sheetField({},i),void 0!==o.sheet_id&&""!==o.sheet_id){var r=n(".wpforms-builder-google-sheets-provider-sheet-id select",i),t=l.cache.getColumns(o.spreadsheet_id,o.sheet_id);let e=r.find('option[value="'+o.sheet_id+'"]');e.length||(r.find("option:last").before(n("<option>",{value:o.sheet_id,text:o.sheet_id})),e=r.find('option[value="'+o.sheet_id+'"]')),e.prop("selected",!0),l.tmpl.customFields(i,t,o.custom_fields),n(".wpforms-builder-google-sheets-provider-connection-fields",i).removeClass("wpforms-hidden"),void l.$holder.trigger("connectionGenerated",[{connection:o}])}else{r=l.cache.providerCache.getById(l.provider,"columns","default");l.tmpl.customFields(i,r,o.custom_fields),l.$holder.trigger("connectionGenerated",[{connection:o}])}},replace(e){var o=l.tmpl.conditional(e),o=l.templates.render("connection",{connection:e,conditional:o,provider:l.provider});l.ui.connection.getById(e.id).length?l.ui.connection.getById(e.id).replaceWith(o):l.$connections.prepend(o)},isNewConnection(e){return _.has(e,"isNew")&&e.isNew},dataLoad(){l.Providers.ajax.request(l.provider,{data:{task:"connections_get"}}).done(function(i){_.isEmpty(i.data.invalid_account)?i.success&&_.has(i.data,"connections")&&(n.each(["columns","conditionals","connections","sheets"],function(e,o){l.cache.providerCache.set(l.provider,o,{}),_.has(i.data,o)&&!_.isEmpty(i.data[o])&&l.cache.providerCache.set(l.provider,o,jQuery.extend({},i.data[o]))}),l.$holder.trigger("connectionsDataLoaded",[i.data])):l.ui.account.invalidAccountModal(i.data.invalid_account)})},refreshConnections(e,o){if(Object.prototype.hasOwnProperty.call(o,"google_sheets")){const i=o.google_sheets;if(_.isEmpty(i.invalid_account)){n.each(["columns","sheets","non_editable_spreadsheets"],function(e,o){_.has(i,o)&&!_.isEmpty(i[o])&&l.cache.providerCache.set(l.provider,o,jQuery.extend(l.cache.providerCache.get(l.provider,o),i[o]))}),_.isEmpty(i.non_editable_spreadsheets)||l.ui.spreadsheetField.nonEditableModal();for(const r in i.connections)l.ui.connection.generate(i.connections[r]);wpf.savedState=wpf.getFormState("#wpforms-builder-form"),WPForms.Admin.Builder.SmartTags.initWidgets(l.$connections)}else l.ui.account.invalidAccountModal(i.invalid_account)}},replaceConnectionIds(o,e){e.find("input, textarea, select, label").each(function(){var e=n(this);e.attr("name")&&e.attr("name",e.attr("name").replace(/%connection_id%/gi,o)),e.attr("id")&&e.attr("id",e.attr("id").replace(/%connection_id%/gi,o)),e.attr("for")&&e.attr("for",e.attr("for").replace(/%connection_id%/gi,o)),e.attr("data-name")&&e.attr("data-name",e.attr("data-name").replace(/%connection_id%/gi,o))})},lock(e){var o=l.ui.connection.getById(e.id),i=n(".js-wpforms-builder-google-sheets-provider-connection-sheet-id",o),r=n(".wpforms-btn-remove-spreadsheet-connection,.wpforms-builder-google-sheets-provider-connection-fields .add,.wpforms-builder-google-sheets-provider-connection-fields .delete,.wpforms-show-smart-tags,.wpforms-conditional-block .wpforms-conditional-rule-add,.wpforms-conditional-block .wpforms-conditional-rule-delete,.wpforms-conditional-block .wpforms-conditional-groups-add,.wpforms-conditional-block .wpforms-conditional-group:last h5",o),t=n(".wpforms-builder-provider-connection-block:eq(0)",o),s=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",o),d=s.val();s.attr("type","text"),i.find("option").remove(),i.append(new Option(e.sheet_id,e.sheet_id,!0)),r.addClass("wpforms-hidden"),o.find("select, input").prop("disabled","disabled"),t.prepend(l.templates.render("connection-error",{email:wpforms_builder.google_sheets_email,spreadsheet_url:l.ui.docLink.getSpreadsheetURL(d,e.sheet_id)})),o.prepend('<input type="hidden" name="'+s.prop("name").replace("spreadsheet_id","__lock__")+'" value="1">')},unlock(e){var o=e.data("connection_id"),i=n(".wpforms-alert",e),o=n('[name="providers[google-sheets]['+o+'][__lock__]"]'),r=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",e),t=n(".wpforms-builder-provider-connection-fields-table-row:eq(0)",e),s=n(".wpforms-btn-remove-spreadsheet-connection,.wpforms-builder-google-sheets-provider-connection-fields .add,.wpforms-builder-google-sheets-provider-connection-fields .delete,.wpforms-show-smart-tags,.wpforms-conditional-block .wpforms-conditional-rule-add,.wpforms-conditional-block .wpforms-conditional-rule-delete,.wpforms-conditional-block .wpforms-conditional-groups-add,.wpforms-conditional-block .wpforms-conditional-group:last h5",e);r.attr("type","hidden"),i.remove(),o.remove(),e.find("select, input").removeAttr("disabled","disabled"),t.find("select, input").prop("disabled","disabled"),s.removeClass("wpforms-hidden")}}},tmpl:{commonsHTML(){return l.templates.render("error")},sheetField(e,o){var i=n(".js-wpforms-builder-google-sheets-provider-connection-spreadsheet-id",o).val();n(".wpforms-builder-google-sheets-provider-sheet-id .wpforms-builder-provider-connection-block-field",o).html(l.templates.render("sheet-select",{provider:l.provider,connection_id:o.data("connection_id"),sheets:e,spreadsheet_id:i}))},customFields(e,o,i){n(".wpforms-builder-google-sheets-provider-connection-fields",e).html(l.templates.render("fields",{connection_id:e.data("connection_id"),fields:l.ui.customFields.getFormFields(),provider:l.provider,columns:o,custom_fields:i})),wpf.initTooltips()},conditional(e){return _.has(e,"conditional")&&!l.ui.connection.isNewConnection(e)?e.conditional:l.templates.render("connection-conditionals")}},modal(e){n.alert({title:wpforms_builder.heads_up,content:e,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})}};return l}(document,(window,jQuery)),WPForms.Admin.Builder.Providers.GoogleSheets.init();