.wpforms-google-sheets-setting-field-redirect-uri {
  font-size: 14px;
  text-align: left;
}

.wpforms-google-sheets-setting-field-redirect-uri-row {
  display: flex;
}

.wpforms-google-sheets-setting-field-redirect-uri-row + .wpforms-google-sheets-setting-field-redirect-uri-row {
  margin-top: 10px;
}

.wpforms-google-sheets-setting-field-redirect-uri label {
  font-size: 14px;
  line-height: 17px;
  display: block;
  margin-bottom: 10px;
}

.wpforms-google-sheets-setting-field-redirect-uri-copy {
  position: relative;
  display: inline-block;
  height: 32px;
  margin: 0 0 0 10px;
  padding: 8px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  background-color: #f8f8f8;
  color: #777777;
  border: 1px solid #cccccc;
  border-radius: 4px;
  box-shadow: none;
  cursor: pointer;
  transition: .1s ease;
}

.wpforms-google-sheets-setting-field-redirect-uri-copy:hover {
  background-color: #d7d7d7;
  color: #444444;
  border-color: #cccccc;
}

.wpforms-google-sheets-setting-field-redirect-uri-copy-icon {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 16px;
  height: 16px;
}

.wpforms-google-sheets-setting-field-redirect-uri-copy-icon-copy:before {
  content: '\f0c5';
}

.wpforms-google-sheets-setting-field-redirect-uri-copy-icon-copied {
  transition: .1s ease;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  left: 0;
  width: 100%;
  opacity: 0;
}

.wpforms-google-sheets-setting-field-redirect-uri-copy-icon-copied:before {
  content: '\f00c';
}

.wpforms-google-sheets-setting-field-redirect-uri-copy-success .wpforms-google-sheets-setting-field-redirect-uri-copy-icon-copy {
  opacity: 0;
}

.wpforms-google-sheets-setting-field-redirect-uri-copy-success .wpforms-google-sheets-setting-field-redirect-uri-copy-icon-copied {
  opacity: 1;
}

#wpforms-google-sheets-sign-in {
  /**
	 * Generated via https://developers.google.com/identity/branding-guidelines#expandable-1
	 */
}

#wpforms-google-sheets-sign-in .gsi-material-button {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -webkit-appearance: none;
  background-color: WHITE;
  background-image: none;
  border: 1px solid #747775;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #1f1f1f;
  cursor: pointer;
  font-family: 'Roboto', arial, sans-serif;
  font-size: 14px;
  height: 40px;
  letter-spacing: 0.25px;
  outline: none;
  overflow: hidden;
  padding: 0 12px;
  position: relative;
  text-align: center;
  -webkit-transition: background-color .218s, border-color .218s, box-shadow .218s;
  transition: background-color .218s, border-color .218s, box-shadow .218s;
  vertical-align: middle;
  white-space: nowrap;
  width: auto;
  max-width: 400px;
  min-width: min-content;
}

#wpforms-google-sheets-sign-in .gsi-material-button .gsi-material-button-icon {
  height: 20px;
  margin-right: 12px;
  min-width: 20px;
  width: 20px;
}

#wpforms-google-sheets-sign-in .gsi-material-button .gsi-material-button-content-wrapper {
  -webkit-align-items: center;
  align-items: center;
  display: flex;
  -webkit-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  flex-wrap: nowrap;
  height: 100%;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

#wpforms-google-sheets-sign-in .gsi-material-button .gsi-material-button-contents {
  -webkit-flex-grow: 1;
  flex-grow: 1;
  font-family: 'Roboto', arial, sans-serif;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
}

#wpforms-google-sheets-sign-in .gsi-material-button .gsi-material-button-state {
  -webkit-transition: opacity .218s;
  transition: opacity .218s;
  bottom: 0;
  left: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
}

#wpforms-google-sheets-sign-in .gsi-material-button:disabled {
  cursor: default;
  background-color: #ffffff61;
  border-color: #1f1f1f1f;
}

#wpforms-google-sheets-sign-in .gsi-material-button:disabled .gsi-material-button-contents {
  opacity: 38%;
}

#wpforms-google-sheets-sign-in .gsi-material-button:disabled .gsi-material-button-icon {
  opacity: 38%;
}

#wpforms-google-sheets-sign-in .gsi-material-button:not(:disabled):active .gsi-material-button-state,
#wpforms-google-sheets-sign-in .gsi-material-button:not(:disabled):focus .gsi-material-button-state {
  background-color: #303030;
  opacity: 12%;
}

#wpforms-google-sheets-sign-in .gsi-material-button:not(:disabled):hover {
  -webkit-box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
}

#wpforms-google-sheets-sign-in .gsi-material-button:not(:disabled):hover .gsi-material-button-state {
  background-color: #303030;
  opacity: 8%;
}

.rtl #wpforms-google-sheets-sign-in .gsi-material-button .gsi-material-button-icon {
  margin-right: 0;
  margin-left: 12px;
}

#wpforms-settings-providers #wpforms-integration-google-sheets.focus-in .wpforms-settings-provider-accounts-connect {
  display: none;
}

#wpforms-integration-google-sheets .wpforms-settings-provider-accounts-toggle {
  font-size: 14px;
  line-height: 1.3;
  color: #8c8f94;
  font-style: italic;
}

#wpforms-integration-google-sheets .wpforms-settings-provider-accounts-toggle a {
  color: inherit;
}

#wpforms-integration-google-sheets .wpforms-settings-provider-accounts-toggle a:hover {
  color: #3c434a;
}

#wpforms-integration-google-sheets .wpforms-google-sheets-setting-field-redirect-uri label {
  margin-bottom: 10px;
}

#wpforms-integration-google-sheets .wpforms-google-sheets-setting-field-redirect-uri-row + .wpforms-google-sheets-setting-field-redirect-uri-row {
  margin-top: 5px;
}

#wpforms-integration-google-sheets .wpforms-google-sheets-setting-field-redirect-uri-copy {
  position: relative;
  display: inline-block;
  min-height: 35px;
  margin-left: 5px;
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  background-color: #eeeeee;
  color: #666666;
  border: 1px solid #cccccc;
  border-radius: 3px;
  box-shadow: none;
  cursor: pointer;
}

#wpforms-integration-google-sheets .wpforms-google-sheets-setting-field-redirect-uri-copy:hover {
  background-color: #d7d7d7;
  border-color: #cccccc;
  color: #444444;
}

#wpforms-integration-google-sheets .wpforms-google-sheets-setting-field-redirect-uri-copy-icon {
  font-size: 16px;
  font-family: dashicons;
  display: block;
  line-height: 1;
  font-weight: 400;
  font-style: normal;
  speak: never;
  text-decoration: inherit;
  text-transform: none;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#wpforms-integration-google-sheets .wpforms-google-sheets-setting-field-redirect-uri-copy-icon-copy:before {
  content: "\f105";
}

#wpforms-integration-google-sheets .wpforms-google-sheets-setting-field-redirect-uri-copy-icon-copied:before {
  content: "\f147";
}

.wpforms-google-sheets-setting-field-redirect-uri {
  margin-bottom: 15px;
  text-align: start;
}

.wpforms-google-sheets-setting-field-redirect-uri label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.wpforms-google-sheets-setting-field-redirect-uri .wpforms-google-sheets-setting-field-redirect-uri-input {
  float: left;
  display: block;
  min-height: 35px;
  width: 100%;
  max-width: 400px;
  padding: 7px 12px;
  line-height: 1.3;
  color: #444444;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  box-shadow: none;
}

.wpforms-google-sheets-setting-field-redirect-uri-copy {
  height: 100%;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
