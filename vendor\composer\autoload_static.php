<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit4d999e349542f80d619e28726c1858ed
{
    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WPFormsGoogleSheets\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WPFormsGoogleSheets\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit4d999e349542f80d619e28726c1858ed::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit4d999e349542f80d619e28726c1858ed::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit4d999e349542f80d619e28726c1858ed::$classMap;

        }, null, ClassLoader::class);
    }
}
